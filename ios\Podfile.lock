PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_localization (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_localization (from `.symlinks/plugins/flutter_localization/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_localization:
    :path: ".symlinks/plugins/flutter_localization/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"

SPEC CHECKSUMS:
  connectivity_plus: ddd7f30999e1faaef5967c23d5b6d503d10434db
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_localization: f43b18844a2b3d2c71fd64f04ffd6b1e64dd54d4
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
