name: callitris
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_svg: ^2.0.9
  google_fonts: ^6.2.1
  image_picker: ^1.0.7
  image: ^4.1.7
  http: ^1.2.0
  path: ^1.8.3
  provider: ^6.1.2
  cached_network_image: ^3.3.1
  connectivity_plus: ^6.0.1
  
  flutter_launcher_icons: ^0.14.0
  skeletonizer: ^1.1.2+1
  syncfusion_flutter_charts: ^27.1.48
  file_picker: ^8.0.0+1
  intl: ^0.19.0
  flutter_localization: ^0.2.2

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo.jpeg"
  min_sdk_android: 21 

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/logo.jpeg
    - assets/logo.png
    - assets/logo_callitris.png
    - assets/macket.jpeg


fonts:
  - family : Poppins
    fonts:
      - asset: fonts/Poppins-Bold.ttf
        weight: 700
      - asset: fonts/Poppins-Medium.ttf
        weight: 500
      - asset: fonts/Poppins-Regular.ttf
        weight: 400
      - asset: fonts/Poppins-SemiBold.ttf
        weight: 600
      
